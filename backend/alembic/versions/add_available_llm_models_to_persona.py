"""Add available_llm_models column to persona table

Revision ID: add_available_llm_models
Revises: ffc707a226b4
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'add_available_llm_models'
down_revision = 'ffc707a226b4'
branch_labels = None
depends_on = None


def upgrade():
    # Add available_llm_models column to persona table
    op.add_column('persona', sa.Column('available_llm_models', postgresql.JSONB(), nullable=True))


def downgrade():
    # Remove available_llm_models column from persona table
    op.drop_column('persona', 'available_llm_models')
